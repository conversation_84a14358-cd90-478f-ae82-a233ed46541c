import 'dart:convert';

class UserProfileResponse {
  final bool? status;
  final String? message;
  final UserProfile? data;

  UserProfileResponse({
    this.status,
    this.message,
    this.data,
  });

  factory UserProfileResponse.fromJson(Map<String, dynamic> json) {
    return UserProfileResponse(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null ? UserProfile.fromJson(json['data']) : null,
    );
  }
}

class UserProfile {
  final int? id;
  final String? fullName;
  final String? dob;
  final String? gender;
  final String? preferedGender;
  final String? preferedSmoking;
  final String? cleaniness;
  final bool? isHavingPet;
  final String? classStanding;
  final List<int>? habitsLifestyle;
  final List<int>? livingStyle;
  final List<int>? interestsHobbies;
  final String? about;
  final String? contactNumber;
  final String? preferedLeasePeriod;
  final List<LatLong>? preferedLocations;
  final String? personalityTypeDescription;
  final bool? isVerified;
  final bool? isActive;
  final String? updatedAt;
  final String? profilePicture;
  final List<String>? profilePictures;

  UserProfile({
    this.id,
    this.fullName,
    this.dob,
    this.gender,
    this.preferedGender,
    this.preferedSmoking,
    this.cleaniness,
    this.isHavingPet,
    this.classStanding,
    this.habitsLifestyle,
    this.livingStyle,
    this.interestsHobbies,
    this.about,
    this.contactNumber,
    this.preferedLeasePeriod,
    this.preferedLocations,
    this.personalityTypeDescription,
    this.isVerified,
    this.isActive,
    this.updatedAt,
    this.profilePicture,
    this.profilePictures,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'],
      fullName: json['full_name'],
      dob: json['dob'],
      gender: json['gender'],
      preferedGender: json['prefered_gender'],
      preferedSmoking: json['prefered_smoking'],
      cleaniness: json['cleaniness'],
      isHavingPet: json['is_having_pet'],
      classStanding: json['class_standing'],
      habitsLifestyle: _parseListInt(json['habits_lifestyle']),
      livingStyle: _parseListInt(json['living_style']),
      interestsHobbies: _parseListInt(json['interests_hobbies']),
      about: json['about'],
      contactNumber: json['contact_number'],
      preferedLeasePeriod: json['prefered_lease_period'],
      preferedLocations: _parseLatLongList(json['prefered_locations']),
      personalityTypeDescription: json['personality_type_description'],
      isVerified: json['is_verified'],
      isActive: json['is_active'],
      updatedAt: json['updated_at'],
      profilePicture: json['profile_picture'],
      profilePictures: (json['profile_pictures'] as List<dynamic>?)
          ?.map((e) => e.toString())
          .toList(),
    );
  }

  static List<int>? _parseListInt(dynamic raw) {
    if (raw == null) return null;
    try {
      final list = raw is String ? jsonDecode(raw) : raw;
      return List<int>.from(list.map((e) => int.tryParse(e.toString()) ?? 0));
    } catch (_) {
      return null;
    }
  }

  static List<LatLong>? _parseLatLongList(dynamic raw) {
    if (raw == null || raw is! String) return null;
    try {
      final cleaned = raw
          .replaceAllMapped(RegExp(r'(\w+)\s*:'), (match) => '"${match[1]}":')
          .replaceAll("'", "\"")
          .replaceAll(",]", "]");
      return List<Map<String, dynamic>>.from(jsonDecode(cleaned))
          .map((e) => LatLong.fromJson(e))
          .toList();
    } catch (_) {
      return null;
    }
  }
}

class LatLong {
  final double? lat;
  final double? long;

  LatLong({this.lat, this.long});

  factory LatLong.fromJson(Map<String, dynamic> json) {
    return LatLong(
      lat: (json['lat'] as num?)?.toDouble(),
      long: (json['long'] as num?)?.toDouble(),
    );
  }
}
