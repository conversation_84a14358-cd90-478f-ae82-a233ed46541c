import 'package:room_eight/core/api_config/endpoints/socket_key.dart';
import 'package:room_eight/core/socket/socket_service.dart';
import 'package:room_eight/core/utils/app_exports.dart';

class RoomEightApp extends StatefulWidget {
  const RoomEightApp({super.key, required this.prefs});
  final Box prefs;

  @override
  State<RoomEightApp> createState() => _RoomEightAppState();
}

class _RoomEightAppState extends State<RoomEightApp> {
  // @override
  // void initState() {
  //   super.initState();
  //   SocketService.initializeSocket();
  // }

  // void didChangeAppLifecycleState(AppLifecycleState state) {
  //   if (state == AppLifecycleState.paused) {
  //     // App is in the background
  //     SocketService.closeConnection();
  //   } else if (state == AppLifecycleState.resumed) {
  //     // App is in the foreground
  //     SocketService.initializeSocket();
  //     Future.delayed(const Duration(seconds: 2), () {
  //       Logger.lOG("RECONNECT");
  //       if (Prefobj.preferences?.get(Prefkeys.AUTHTOKEN) != null) {
  //         SocketService.emit(SocketConfig.joinSocket, {
  //           'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
  //         });
  //         SocketService.response(SocketConfig.joinSocket, (joinSocket) {});
  //       }
  //     });
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    Prefobj.preferences = widget.prefs;
    FlutterNativeSplash.remove();
    return BlocProviders(
      child: MultiBlocListener(
        listeners: [
          BlocListener<CheckConnectionCubit, CheckConnectionStates>(
            listener: (context, state) {
              if (state is InternetDisconnected) {
                CheckConnectionCubit.get(context).isNetDialogShow = true;
                Logger.lOG('InternetDisconnected');
                Future.delayed(const Duration(milliseconds: 500), () {});
              }
              if (state is InternetConnected) {
                if (CheckConnectionCubit.get(context).isNetDialogShow) {
                  Logger.lOG('InternetConnected');

                  CheckConnectionCubit.get(context).isNetDialogShow = false;
                }
              }
            },
          ),
        ],
        child: BlocBuilder<LocaleBloc, LocaleState>(
          builder: (context, localeState) {
            return MediaQuery(
              data: MediaQuery.of(context).copyWith(
                highContrast: true,
                displayFeatures: MediaQuery.of(context).displayFeatures,
                gestureSettings: MediaQuery.of(context).gestureSettings,
                textScaler: TextScaler.noScaling,
                invertColors: false,
                boldText: false,
              ),
              child: GestureDetector(
                onTap: () {
                  FocusManager.instance.primaryFocus?.unfocus();
                },
                child: ScreenUtilInit(
                  designSize: Size(430, 932),
                  minTextAdapt: true,
                  splitScreenMode: true,
                  child: ToastificationWrapper(
                    child: MaterialApp(
                      title: 'Room 8',
                      builder: OneContext().builder,
                      debugShowCheckedModeBanner: false,
                      localizationsDelegates: const [
                        Lang.delegate,
                        GlobalMaterialLocalizations.delegate,
                        GlobalWidgetsLocalizations.delegate,
                        GlobalCupertinoLocalizations.delegate,
                      ],
                      supportedLocales: Lang.delegate.supportedLocales,
                      navigatorKey: NavigatorService.navigatorKey,
                      locale: localeState.locale,
                      theme: MyAppThemeHelper.lightTheme,
                      themeMode: ThemeMode.light,
                      initialRoute: AppRoutes.initialRoute,
                      routes: AppRoutes.routes,
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
