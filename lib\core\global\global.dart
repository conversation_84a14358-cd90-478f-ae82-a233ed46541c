import 'dart:io';

import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

final Uuid uuid = Uuid();

Future<File> urlToFile(String imageUrl) async {
  try {
    final response = await http.get(Uri.parse(imageUrl));

    if (response.statusCode != 200) {
      throw Exception('Failed to download image: ${response.statusCode}');
    }

    final documentDirectory = await getTemporaryDirectory();
    final fileName = imageUrl
        .split('/')
        .last
        .split('?')
        .first; // Remove query parameters
    final file = File('${documentDirectory.path}/$fileName');

    return await file.writeAsBytes(response.bodyBytes);
  } catch (e) {
    throw Exception('Error converting URL to file: $e');
  }
}
