import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/widgets/common_widget/common_shimmer.dart';

class ProfileScreenShimmer extends StatelessWidget {
  const ProfileScreenShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: CommonShimmer(
        baseColor:
            Theme.of(
              context,
            ).customColors.lightGreyTextColor?.withValues(alpha: 0.3) ??
            Colors.grey[300]!,
        highlightColor:
            Theme.of(
              context,
            ).customColors.lightGreyTextColor?.withValues(alpha: 0.1) ??
            Colors.grey[100]!,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeaderInfoShimmer(),
            buildSizedBoxH(16.h),
            _buildGenderAndAgeRowShimmer(),
            buildSizedBoxH(16.h),
            _buildPersonalityContainerShimmer(),
            buildSizedBoxH(16.h),
            _buildContactNumberShimmer(),
            buildSizedBoxH(16.h),
            _buildLeasePeriodFieldShimmer(),
            buildSizedBoxH(16.h),
            _buildPhotoGalleryShimmer(),
            buildSizedBoxH(110.h),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderInfoShimmer() {
    return Column(
      children: [
        Center(
          child: Stack(
            alignment: Alignment.center,
            children: [
              ShimmerBox(
                height: 130.h,
                width: 130.h,
                borderRadius: BorderRadius.circular(65.h),
              ),
              ShimmerCircle(size: 100.h),
            ],
          ),
        ),
        buildSizedBoxH(16.h),
        ShimmerLine(width: 150.w, height: 20.h),
        buildSizedBoxH(4.h),
        ShimmerLine(width: 200.w, height: 14.h),
      ],
    );
  }

  Widget _buildGenderAndAgeRowShimmer() {
    return Row(
      children: [
        Expanded(
          child: ShimmerCard(
            height: 80.h,
            child: Row(
              children: [
                ShimmerCircle(size: 48.h),
                buildSizedboxW(16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ShimmerLine(width: 60.w, height: 14.h),
                      buildSizedBoxH(4.h),
                      ShimmerLine(width: 40.w, height: 16.h),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        buildSizedboxW(16.w),
        Expanded(
          child: ShimmerCard(
            height: 80.h,
            child: Row(
              children: [
                ShimmerCircle(size: 48.h),
                buildSizedboxW(16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ShimmerLine(width: 40.w, height: 14.h),
                      buildSizedBoxH(4.h),
                      ShimmerLine(width: 30.w, height: 16.h),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPersonalityContainerShimmer() {
    return ShimmerCard(
      width: double.infinity,
      height: 120.h,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ShimmerLine(width: 100.w, height: 16.h),
          buildSizedBoxH(8.h),
          Expanded(child: _buildUserTagsShimmer()),
        ],
      ),
    );
  }

  Widget _buildUserTagsShimmer() {
    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      children: List.generate(
        6, // Generate 6 shimmer tags
        (index) => ShimmerBox(
          width: (index % 3 == 0)
              ? 80.w
              : (index % 2 == 0)
              ? 100.w
              : 60.w, // Varied widths
          height: 32.h,
          borderRadius: BorderRadius.circular(16.r),
        ),
      ),
    );
  }

  Widget _buildContactNumberShimmer() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ShimmerLine(width: 120.w, height: 16.h),
        buildSizedBoxH(8.h),
        ShimmerBox(
          width: double.infinity,
          height: 50.h,
          borderRadius: BorderRadius.circular(12.r),
        ),
      ],
    );
  }

  Widget _buildLeasePeriodFieldShimmer() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ShimmerLine(width: 100.w, height: 16.h),
        buildSizedBoxH(8.h),
        ShimmerBox(
          width: double.infinity,
          height: 50.h,
          borderRadius: BorderRadius.circular(12.r),
        ),
      ],
    );
  }

  Widget _buildPhotoGalleryShimmer() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ShimmerLine(width: 110.w, height: 16.h),
        buildSizedBoxH(8.h),
        SizedBox(
          height: 90.h,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: 4, // Show 4 shimmer photos
            separatorBuilder: (_, __) => SizedBox(width: 12.w),
            itemBuilder: (context, index) {
              return ShimmerBox(
                width: 70.w,
                height: 80.h,
                borderRadius: BorderRadius.circular(12.r),
              );
            },
          ),
        ),
      ],
    );
  }
}
