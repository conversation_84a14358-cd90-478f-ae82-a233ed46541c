import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/viewmodels/like_bloc/like_bloc.dart';

class LikeScreen extends StatelessWidget {
  const LikeScreen({super.key});
  static Widget builder(BuildContext context) => const LikeScreen();

  @override
  Widget build(BuildContext context) {
    // context.read<LikeBloc>().add(LoadLikeAndDisLikeData());
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: Theme.of(context).customColors.scaffoldColor,
        body: BlocBuilder<LikeBloc, LikeState>(
          builder: (context, state) {
            if (state.isLoadData) {
              return CircularProgressIndicator();
            }
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.r),
              child: Column(
                children: [
                  buildSizedBoxH(50.h),
                  _buildTabView(context),
                  Expanded(
                    child: PageView(
                      controller: context.read<LikeBloc>().pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        _buildMoveOutView(context),
                        _buildMoveInView(context),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTabView(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
      decoration: BoxDecoration(
        color: Theme.of(
          context,
        ).customColors.fillColor, // <-- your desired background color
        borderRadius: BorderRadius.circular(
          100.r,
        ), // optional, if you want rounded background
      ),
      child: TabBar(
        onTap: (index) => context.read<LikeBloc>().add(TabChangedEvent(index)),
        indicatorSize: TabBarIndicatorSize.tab,
        labelStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
          fontSize: 16.sp,
          fontWeight: FontWeight.w500,
        ),
        labelColor: Theme.of(context).customColors.fillColor,
        dividerColor: Colors.transparent,
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        labelPadding: EdgeInsets.all(0.r),
        indicator: ShapeDecoration(
          color: Theme.of(context).customColors.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(100.r),
          ),
        ),
        tabs: [
          Tab(text: Lang.of(context).lbl_move_out),
          Tab(text: Lang.of(context).lbl_move_in),
        ],
      ),
    );
  }

  Widget _buildMoveOutView(BuildContext context) {
    return Container();
  }

  Widget _buildMoveInView(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Column(
            children: [
              buildSizedBoxH(20.h),
              buildUserProfileCard(
                context: context,
                imageUrl:
                    "https://images.ctfassets.net/h6goo9gw1hh6/2sNZtFAWOdP1lmQ33VwRN3/24e953b920a9cd0ff2e1d587742a2472/1-intro-photo-final.jpg?w=1200&h=992&fl=progressive&q=70&fm=jpg",
                name: 'Cameron Williamson',
                department: 'Data Science',
                onLike: () {
                  // Handle like
                },
                onDislike: () {
                  // Handle dislike
                },
                onFavorite: () {
                  // Handle favorite
                },
              ),
              buildSizedBoxH(20.h),
              buildUserProfileCard(
                context: context,
                imageUrl:
                    "https://images.ctfassets.net/h6goo9gw1hh6/2sNZtFAWOdP1lmQ33VwRN3/24e953b920a9cd0ff2e1d587742a2472/1-intro-photo-final.jpg?w=1200&h=992&fl=progressive&q=70&fm=jpg",
                name: 'Cameron Williamson',
                department: 'Data Science',
                onLike: () {
                  // Handle like
                },
                onDislike: () {
                  // Handle dislike
                },
                onFavorite: () {
                  // Handle favorite
                },
              ),
              buildSizedBoxH(20.h),
              buildUserProfileCard(
                context: context,
                imageUrl:
                    "https://images.ctfassets.net/h6goo9gw1hh6/2sNZtFAWOdP1lmQ33VwRN3/24e953b920a9cd0ff2e1d587742a2472/1-intro-photo-final.jpg?w=1200&h=992&fl=progressive&q=70&fm=jpg",
                name: 'Cameron Williamson',
                department: 'Data Science',
                onLike: () {
                  // Handle like
                },
                onDislike: () {
                  // Handle dislike
                },
                onFavorite: () {
                  // Handle favorite
                },
              ),
              buildSizedBoxH(20.h),
              buildUserProfileCard(
                context: context,
                imageUrl:
                    "https://images.ctfassets.net/h6goo9gw1hh6/2sNZtFAWOdP1lmQ33VwRN3/24e953b920a9cd0ff2e1d587742a2472/1-intro-photo-final.jpg?w=1200&h=992&fl=progressive&q=70&fm=jpg",
                name: 'Cameron Williamson',
                department: 'Data Science',
                onLike: () {
                  // Handle like
                },
                onDislike: () {
                  // Handle dislike
                },
                onFavorite: () {
                  // Handle favorite
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildUserProfileCard({
    required BuildContext context,
    required String imageUrl,
    required String name,
    required String department,
    required VoidCallback onLike,
    required VoidCallback onDislike,
    required VoidCallback onFavorite,
  }) {
    return Container(
      padding: EdgeInsets.all(8.r),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.fillColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(color: Colors.black12, blurRadius: 8, offset: Offset(0, 4)),
        ],
      ),
      child: Column(
        children: [
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(12.r),
                child: Image.network(
                  imageUrl,
                  height: 180.h,
                  width: double.infinity,
                  fit: BoxFit.cover,
                ),
              ),
              Positioned(
                top: 10.h,
                right: 10.w,
                child: InkWell(
                  onTap: onFavorite,
                  child: CircleAvatar(
                    radius: 20.r,
                    backgroundColor: Colors.white70,
                    child: CustomImageView(
                      imagePath: Assets.images.svgs.icons.icLike.path,
                      margin: EdgeInsets.all(10.r),
                    ),
                  ),
                ),
              ),
            ],
          ),
          buildSizedBoxH(12.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                name,
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16.sp),
              ),
              Row(
                children: [
                  Container(
                    height: 30.h,
                    width: 30.w,
                    decoration: BoxDecoration(
                      color: Theme.of(context).customColors.fillColor,
                      borderRadius: BorderRadius.circular(100.r),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.shade300,
                          blurRadius: 5.r,
                          offset: Offset(1, 2),
                        ),
                      ],
                    ),
                    child: CustomImageView(
                      imagePath: Assets.images.svgs.icons.icClose.path,
                      margin: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 8.h,
                      ),
                    ),
                  ),
                  buildSizedboxW(5.w),
                  Container(
                    height: 30.h,
                    width: 30.w,
                    decoration: BoxDecoration(
                      color: Theme.of(context).customColors.fillColor,
                      borderRadius: BorderRadius.circular(100.r),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.shade300,
                          blurRadius: 5.r,
                          offset: Offset(1, 2),
                        ),
                      ],
                    ),
                    child: CustomImageView(
                      imagePath: Assets.images.svgs.icons.icWirite.path,
                      margin: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 8.h,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          Row(
            children: [
              Text(
                department,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontSize: 14.sp,
                  color: Theme.of(context).customColors.darkGreytextcolor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
