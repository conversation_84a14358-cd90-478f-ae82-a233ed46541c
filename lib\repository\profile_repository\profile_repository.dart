import 'package:flutter/foundation.dart';
import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/models/auth_model/profile_detail_model.dart';
import 'package:room_eight/models/profile_model/profile_model.dart';
import 'package:room_eight/models/user_profile_model/user_profile_model.dart';

class ProfileRepository {
  final ApiClient apiClient;
  ProfileRepository({required this.apiClient});

  Future<ProfileModel> getSelectionMenu() async {
    try {
      var response = await apiClient.request(
        RequestType.GET,
        ApiEndPoint.getSelectionMenuUrl,
      );
      return ProfileModel.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }

  Future<ProfileDetailResponse> profileDetailCall({
    required String profilePicturePath,
    required List<String> profilePictures,
    required String fullName,
    required String dob,
    required String gender,
    required String preferredGender,
    required String preferredSmoking,
    required String cleanliness,
    required bool isHavingPet,
    required String classStanding,
    required String habitsLifestyle,
    required String livingStyle,
    required String interestsHobbies,
    required String about,
    required String contactNumber,
    required String preferredLeasePeriod,
    required String preferredLocations,
    required String personalityTypeDescription,
  }) async {
    try {
      final data = <String, dynamic>{
        "profile_picture": profilePicturePath,
        "profile_pictures": profilePictures,
        "full_name": fullName,
        "dob": dob,
        "gender": gender,
        "prefered_gender": preferredGender,
        "prefered_smoking": preferredSmoking,
        "cleaniness": cleanliness,
        "is_having_pet": isHavingPet,
        "class_standing": classStanding,
        "habits_lifestyle": habitsLifestyle,
        "living_style": livingStyle,
        "interests_hobbies": interestsHobbies,
        "about": about,
        "contact_number": contactNumber,
        "prefered_lease_period": preferredLeasePeriod,
        "prefered_locations": preferredLocations,
        "personality_type_description": personalityTypeDescription,
      };

      Logger.lOG("Profile data being sent:");
      data.forEach((key, value) {
        if (key == 'profile_picture') {
          Logger.lOG("$key: $value (single image file)");
        } else if (key == 'profile_pictures') {
          Logger.lOG("$key: $value (list of ${(value as List).length} images)");
        } else {
          Logger.lOG("$key: $value");
        }
      });

      var response = await apiClient.request(
        RequestType.MULTIPART_POST,
        ApiEndPoint.createUserProfileUrl,
        multipartData: data,
      );
      return await compute(ProfileDetailResponse.fromJson, response);
    } catch (error) {
      rethrow;
    }
  }

  Future<UserProfileResponse> userProfileCall() async {
    try {
      var response = await apiClient.request(
        RequestType.GET,
        ApiEndPoint.userProfileUrl,
      );
      return await compute(UserProfileResponse.fromJson, response);
    } catch (error) {
      rethrow;
    }
  }

  Future<ProfileDetailResponse> editProfileCall({
    required String profilePicturePath,
    required List<String> profilePictures,
    required List<String> removedPhotoPaths,
    required String fullName,
    required String dob,
    required String gender,
    required String preferredGender,
    required String preferredSmoking,
    required String cleanliness,
    required bool isHavingPet,
    required String classStanding,
    required String habitsLifestyle,
    required String livingStyle,
    required String interestsHobbies,
    required String about,
    required String contactNumber,
    required String preferredLeasePeriod,
    required String preferredLocations,
    required String personalityTypeDescription,
  }) async {
    try {
      final data = <String, dynamic>{
        "profile_picture": profilePicturePath,
        "full_name": fullName,
        "dob": dob,
        "gender": gender,
        "prefered_gender": preferredGender,
        "prefered_smoking": preferredSmoking,
        "cleaniness": cleanliness,
        "is_having_pet": isHavingPet,
        "class_standing": classStanding,
        "habits_lifestyle": habitsLifestyle,
        "living_style": livingStyle,
        "interests_hobbies": interestsHobbies,
        "about": about,
        "contact_number": contactNumber,
        "prefered_lease_period": preferredLeasePeriod,
        "prefered_locations": preferredLocations,
        "personality_type_description": personalityTypeDescription,
        // "profile_pictures_id": removedPhotoPaths,
      };

      if (profilePictures.isNotEmpty) {
        data["profile_pictures"] = profilePictures;
      }

      var response = await apiClient.request(
        RequestType.MULTIPART_POST,
        ApiEndPoint.editProfileUrl,
        multipartData: data,
      );
      return await compute(ProfileDetailResponse.fromJson, response);
    } catch (error) {
      rethrow;
    }
  }
}
